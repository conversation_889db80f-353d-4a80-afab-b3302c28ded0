<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'CustomerCategoriesBox'
})

const props = defineProps({
  categoriesData: {
    type: Array,
    default: () => ([])
  },
  currentCategory: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['change-current-category'])

const statistics = ref({})
const loading = ref(false)


/**
 * @description 切换当前选中的客户分类
 * @param category
 */
const changeCurrentCategory = (category) => {
  emits('change-current-category', category.scaleId)
}

</script>

<template>
  <div class="customer-categories-boxes__container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>加载客户分类数据中...</span>
    </div>

    <!-- 主要内容 -->
    <template v-else>
      <div class="categories-list">
        <div
          v-for="category in categoriesData"
          :key="category.categoryId"
          class="category-item"
          :class="currentCategory === category.scaleId ? 'category-item-selected' : ''"
          @click="changeCurrentCategory(category)"
        >
          <div class="category-info">
            <div class="category-label">{{ category.scaleName }}红灯客户占比</div>
          </div>

          <div class="category-ratio">
            <div class="ratio-text">
              <span class="ratio-count">
                {{category.redCustomerCount}}
              </span>
              <span class="ratio-symbol">/</span>
              <span class="ratio-total">
                {{category.scaleCustomerCount}}
              </span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.customer-categories-boxes__container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #fff;
  border-radius: 8px;
  overflow-x: auto;
  overflow-y: hidden;

  &::-webkit-scrollbar {
    height: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  // 加载状态样式
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 78px;
    color: #666;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 12px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }


  .categories-list {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
  }

  .category-item {
    min-width: 280px;
    height: 80px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    box-sizing: border-box;
    cursor: pointer;
    padding: 10px 20px 10px 12px;
    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .category-info {
      height: 20px;
      line-height: 20px;
      margin-bottom: 4px;

      .category-label {
        font-size: 14px;
        font-weight: normal;
        color: $text-plain;
      }
    }



    .category-ratio {
      width: 208px;
      height: 34px;
      line-height: 34px;
      font-size: 24px;
      font-weight: 600;
      letter-spacing: normal;
      /* 文字/Text-4 强调正文标题 */
      /* 样式描述：$text-main */
      color: $text-main;
      .ratio-text {
        .ratio-count {

        }
        .ratio-total {

        }
      }
    }
  }

  .category-item-selected {
    background: $brand-color-1;
    border: 1px solid $brand-base-color-6;
    transition: all 0.3s ease;
    .category-label{
      color: $brand-base-color-6;
      font-weight: normal;
    }
    &::before {
      transition: all 0.3s ease;
      content: '';
      position: absolute;
      top: 0px;
      right: 0px;
      width: 28px;
      height: 28px;
      background-image: url('@/assets/bi-chart/select-top-corner.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-top-right-radius: 3px;
    }
  }
}
</style>
