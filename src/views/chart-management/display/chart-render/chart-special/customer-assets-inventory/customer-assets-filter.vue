<script setup>
import { computed, nextTick, reactive, ref } from 'vue'
import MultiTag from '@/components/select-tree/multi-tag.vue'
import PersonSelect from '@/components/person-select' // 人员和部门多选

defineOptions({
  name: 'CustomerAssetsFilter'
})

// 定义props
const props = defineProps({
  companyStructType: {
    type: Number,
    default: 1
  },
  specialParam: {
    type: Boolean,
    default: false
  },
  templateList: {
    type: Array,
    default: () => []
  },
  filterForm: {
    type: Object,
    default: () => ({})
  }
})

// 定义emits
const emit = defineEmits(['filter-change'])
const currentFilterForm = computed(() => {
  return props.filterForm
})

const emitFilterChange = () => {
  emit('filter-change', {
    ...currentFilterForm.value
  })
}

// 部门选择相关
const departmentTags = ref([])
const multiFormat = reactive({
  label: 'name'
})

// 跟进风险选项 全部、红灯客户、绿灯客户
const riskOptions = ref([
  { value: 0, label: '全部' },
  { value: 1, label: '红灯客户' },
  { value: 2, label: '绿灯客户' }
])

// 客户模板选项
const templateOptions = computed(() => {
  return props.templateList.map((item) => ({
    value: item.formId,
    label: item.formName
  }))
})

const personSubmit = (data) => {
  debugger
  departmentTags.value = data
  currentFilterForm.value.checkedId = data
  emitFilterChange()
}

/**
 * 根据公司结构类型决定显示哪些选项卡
 * 当公司结构类型为1时，显示部门和用户选项卡
 * 当公司结构类型为2时，只显示部门选项卡
 * @returns {Array} 要显示的选项卡名称数组
 */
const showTabsName = computed(() => {
  return ['dept']
})

/**
 * 打开组织架构选择弹窗
 * 如果仪表盘处于全屏状态，则不打开弹窗
 */
function personSelectVisitOpen() {
  // 仪表盘全屏时禁止弹出
  if (+utils.SS.get('chart-panel-fullscreen') === 1) {
    return
  }
  const defaultVal = currentFilterForm.value?.checkedId || []
  PersonSelect({
    showTabsName: showTabsName,
    defaultVal: defaultVal,
    tabMultiple: true,
    otherParams: {
      // 是否控制组织架构权限范围
      deptVisibleRule: true,
      parentStrictly: true
    }
  }).then((res) => {
    if (res.data) {
      personSubmit(res.data)
    }
  })
}

const tagDelete = (dep) => {
  nextTick(() => {
    departmentTags.value = departmentTags.value.filter((item) => {
      return dep.id !== item.id
    })
    currentFilterForm.value.checkedId = departmentTags.value
  })
  emitFilterChange()
}

const onRiskChange = () => {
  emitFilterChange()
}

const onTemplateChange = () => {
  emitFilterChange()
}
</script>

<template>
  <div class="customer-assets-filter">
    <div class="filter-container">
      <!-- 部门筛选 -->
      <div class="filter-item">
        <span class="filter-label">部门</span>
        <multi-tag
          class="filter-multi-select"
          :prop-format="multiFormat"
          :selected-tag="departmentTags"
          @click.native="personSelectVisitOpen"
          @tagDelete="tagDelete"
        />
      </div>

      <!-- 跟进风险筛选 -->
      <div class="filter-item">
        <span class="filter-label">跟进风险</span>
        <el-select
          v-model="currentFilterForm.argTwoId"
          class="filter-select"
          placeholder="请选择跟进风险"
          @change="onRiskChange"
        >
          <el-option
            v-for="item in riskOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <!-- 客户模板筛选 -->
      <div class="filter-item">
        <span class="filter-label">客户模板</span>
        <el-select
          v-model="currentFilterForm.refId"
          class="filter-select"
          placeholder="请选择客户模板"
          @change="onTemplateChange"
        >
          <el-option
            v-for="item in templateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.customer-assets-filter {
  .filter-container {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
  }

  .filter-item {
    width: 280px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .filter-label {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    min-width: 60px;
  }

  .filter-multi-select {
    min-width: 200px;
    cursor: pointer;
  }

  .filter-select {
    width: 200px;
  }
}
</style>
