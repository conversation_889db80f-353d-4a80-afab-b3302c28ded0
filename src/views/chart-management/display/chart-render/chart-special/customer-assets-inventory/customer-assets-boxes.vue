<script setup>
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { getSpecialPerformance } from '@/api/statistics'

defineOptions({
  name: 'CustomerAssetsBoxes'
})

const props = defineProps({
  filterForm: {
    type: Object,
    default: () => ({})
  },
  chartExplain: {
    type: Object,
    default: () => ({})
  },
})

// 响应式数据
const employeesData = ref([])
const loading = ref(false)
const expandedEmployees = ref(new Set()) // 存储展开的员工ID
const stickyEmployee = ref(null) // 当前吸顶的员工数据
const scrollContainer = ref(null) // 滚动容器引用
const assetsContainer = ref(null) // 资产容器引用
const itemsPerRow = ref(6) // 每行可显示的资产数量，默认6个
let throttleTimer = null // 节流定时器
let resizeObserver = null // 尺寸监听器

// 分页配置项
const pageOption = reactive({
  pageTotal: 0,
  leftPageNums: [],
  rightPageNums: [],
  hasLeft: false,
  hasRight: true,
  currentPageNum: 1, // 当前页的页码
  pageSize: 20, // 每页的大小
  pageSizes: [10, 20, 30, 40, 50, 100],
  rowsCount: 0 // 总的条数
})


// 方法：切换员工资产展开/收起状态
const toggleEmployeeAssets = (employeeId) => {
  if (expandedEmployees.value.has(employeeId)) {
    expandedEmployees.value.delete(employeeId)
  } else {
    expandedEmployees.value.add(employeeId)
  }
}

// 方法：检查员工是否展开
const isEmployeeExpanded = (employeeId) => {
  return expandedEmployees.value.has(employeeId)
}

// 方法：计算每行可显示的资产数量
const calculateItemsPerRow = () => {
  if (!assetsContainer.value) return

  const containerWidth = assetsContainer.value.offsetWidth
  const itemWidth = 168 // asset-box-item 宽度
  const gap = 8 // gap 间距
  const totalItemWidth = itemWidth + gap

  // 计算可以放置多少个完整的item（至少1个）
  const calculatedItems = Math.max(1, Math.floor(containerWidth / totalItemWidth))
  itemsPerRow.value = calculatedItems

  console.log('计算每行显示数量:', {
    containerWidth,
    itemWidth,
    gap,
    totalItemWidth,
    calculatedItems
  })
}

// 方法：获取员工应该显示的客户列表
const getDisplayedCustomers = (employee) => {
  if (!employee.customerList || employee.customerList.length === 0) {
    return []
  }

  const isExpanded = isEmployeeExpanded(employee.userId)

  // 调试信息
  console.log(`员工 ${employee.userName} (${employee.userId}):`, {
    isExpanded,
    customerCount: employee.customerList.length,
    itemsPerRow: itemsPerRow.value,
    shouldShowAll: isExpanded || employee.customerList.length <= itemsPerRow.value
  })

  if (isExpanded || employee.customerList.length <= itemsPerRow.value) {
    return employee.customerList
  }

  return employee.customerList.slice(0, itemsPerRow.value)
}

// 方法：检查员工是否需要显示折叠按钮
const shouldShowToggleButton = (employee) => {
  const shouldShow = employee.customerList && employee.customerList.length > itemsPerRow.value
  console.log(`员工 ${employee.userName} 是否显示折叠按钮:`, {
    customerCount: employee.customerList?.length || 0,
    itemsPerRow: itemsPerRow.value,
    shouldShow
  })
  return shouldShow
}

// 方法：格式化跟进状态文本
const formatFollowUpStatus = (asset) => {
  return `${asset.daysNotFollowed} 天未跟进`
}

// 方法：处理滚动事件，计算吸顶元素
const handleScroll = () => {
  if (!scrollContainer.value) return

  const containerRect = scrollContainer.value.getBoundingClientRect()
  const containerTop = containerRect.top

  // 找到当前应该吸顶的员工
  let targetEmployee = null
  const employeeBoxes = scrollContainer.value.querySelectorAll('.customer-assets-box')

  for (let i = 0; i < employeeBoxes.length; i++) {
    const box = employeeBoxes[i]
    const rect = box.getBoundingClientRect()
    const staffInfoBox = box.querySelector('.staff-info-box')

    if (staffInfoBox && rect.top <= containerTop && rect.bottom > containerTop + 40) {
      const employeeId = staffInfoBox.getAttribute('data-employee-id')
      // 找到对应的员工数据
      targetEmployee = employeesData.value.find((emp) => emp.userId.toString() === employeeId)

      break
    }
  }

  // 只有当目标员工发生变化时才更新
  const currentEmployeeId = stickyEmployee.value?.userId
  const targetEmployeeId = targetEmployee?.userId

  if (currentEmployeeId !== targetEmployeeId) {
    stickyEmployee.value = targetEmployee

    // 设置吸顶元素的位置和宽度
    nextTick(() => {
      if (targetEmployee) {
        const stickyElement = document.querySelector('.temp-staff-info-box')
        if (stickyElement) {
          stickyElement.style.top = containerRect.top + 'px'
          stickyElement.style.left = containerRect.left + 'px'
          stickyElement.style.width = containerRect.width + 'px'
        }
      }
    })
  }
}

// 方法：节流处理滚动事件
const throttledHandleScroll = () => {
  if (throttleTimer) return

  throttleTimer = setTimeout(() => {
    handleScroll()
    throttleTimer = null
  }, 16) // 约60fps
}

/**
 * 生成API请求参数
 * @param {boolean} isExport - 是否用于导出
 * @returns {Object} 请求参数对象
 */
const genCustomerAssetsParams = (isExport = false) => {
  const { filterForm, chartExplain } = props
  const params = {
    page: pageOption.currentPageNum,
    pageSize: pageOption.pageSize,
    companyStructType: 2,
    checkSubDep: filterForm.checkSubDep,
    timeFilter: filterForm.timeRange,
    checkedId: filterForm.checkedId,
    checkedDepId: filterForm.checkedDepId || '',
    statisticsType: chartExplain.statisticsType,
    argStrIn: [filterForm.argStrIn],
    chartIdIn: [
      {
        id: chartExplain.id,
        statisticsType: chartExplain.statisticsType,
        systemCode: chartExplain.systemCode
      }
    ]
  }

  // 选择部门目标时，部门id必传，默认组织架构的第一个部门id
  if (params.companyStructType === 2 && params.checkedDepId === '') {
    params.checkedDepId = 1
  }

  return params
}

/**
 * @description 设置分页组件
 * @param pageHelper
 */
const setPageOption = (pageHelper) => {
  Object.keys(pageOption).forEach(key => {
    pageOption[key] = pageHelper[key]
  })
}

/**
 * @description 获取客户资产盘点数据
 * @return {Promise<void>}
 */
const loadCustomerAssets = async () => {
  const { filterForm, chartExplain } = props

  loading.value = true
  try {
    const params = genCustomerAssetsParams()
    const { result, code } = await getSpecialPerformance(params)
    if (code === 1) {
      employeesData.value = result.chartList[0].customerAsset.userList
      setPageOption(result.chartList[0].customerAsset.pageHelper)
    }
  } catch (error) {
    console.error('获取员工资产数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 方法：初始化滚动监听
const initScrollListener = () => {
  // 查找滚动容器（父组件中的 .customer-assets-boxes__wrapper）
  nextTick(() => {
    const element = document.querySelector('.customer-assets-boxes__wrapper')
    if (element) {
      scrollContainer.value = element
      element.addEventListener('scroll', throttledHandleScroll, { passive: true })
      // 初始化时也执行一次
      handleScroll()
    }
  })
}

// 方法：初始化尺寸监听
const initResizeObserver = () => {
  nextTick(() => {
    const element = document.querySelector('.customer-assets-boxes')
    if (element && window.ResizeObserver) {
      assetsContainer.value = element

      resizeObserver = new ResizeObserver(() => {
        calculateItemsPerRow()
      })

      resizeObserver.observe(element)
      // 初始化时计算一次
      calculateItemsPerRow()
    }
  })
}

// 方法：清理滚动监听
const cleanupScrollListener = () => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', throttledHandleScroll)
  }
  if (throttleTimer) {
    clearTimeout(throttleTimer)
    throttleTimer = null
  }
}

// 方法：清理尺寸监听
const cleanupResizeObserver = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}



// 监听 filterForm对象如果变更要重新加载数据
watch(() => props.filterForm, (newVal, oldVal) => {
    loadCustomerAssets()
}, { deep: true })

// 生命周期：组件挂载时获取数据和初始化滚动监听
onMounted(() => {
  loadCustomerAssets()
  initScrollListener()
  initResizeObserver()
})

// 生命周期：组件卸载时清理监听
onUnmounted(() => {
  cleanupScrollListener()
  cleanupResizeObserver()
})
</script>

<template>
  <div class="customer-assets-boxes">
    <!-- 吸顶员工信息组件 -->
    <div
      v-if="stickyEmployee"
      class="temp-staff-info-box"
      @click="toggleEmployeeAssets(stickyEmployee.userId)"
    >
      <div class="staff-info-left">
        <img class="staff-info-box__avtar" src="@/assets/user-default.png" />
        <span class="staff-info-box__name">{{ stickyEmployee.userName }}</span>
        <span class="staff-info-box__department">{{ stickyEmployee.userId }}</span>
        <span class="staff-info-box__red-count">
          红灯客户：{{ stickyEmployee.redCustomerCount }}
        </span>
      </div>
      <div class="staff-info-right">
        <span v-if="shouldShowToggleButton(stickyEmployee)" class="expand-icon" :class="{ expanded: isEmployeeExpanded(stickyEmployee.userId) }">
          <i class="icon-arrow-down-s-line t-iconfont" />
        </span>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <div v-else-if="employeesData.length === 0" class="empty-container">
      <div class="empty-text">暂无红灯客户数据</div>
    </div>

    <template v-else>
      <!-- 遍历有红灯客户的员工 -->
      <div
        v-for="(employee, idx) in employeesData"
        :key="employee.userId"
        class="customer-assets-box"
      >
        <!-- 员工信息 点击展开 assets-box -->
        <div
          class="staff-info-box"
          :data-employee-id="employee.userId"
          @click="toggleEmployeeAssets(employee.userId)"
        >
          <div class="staff-info-left">
            <img class="staff-info-box__avtar" src="@/assets/user-default.png" />
            <span class="staff-info-box__name">{{ employee.userName }}</span>
            <span class="staff-info-box__department">{{ employee.userId }}</span>
            <span class="staff-info-box__red-count">
              红灯客户：{{ employee.redCustomerCount }}
            </span>
          </div>
          <div class="staff-info-right">
            <span v-if="shouldShowToggleButton(employee)" class="expand-icon" :class="{ expanded: isEmployeeExpanded(employee.userId) }">
              <i class="icon-arrow-down-s-line t-iconfont" />
            </span>
          </div>
        </div>

        <!-- 可折叠的资产列表， 只有员工所拥有的客户数量大于一行才可以折叠 -->
        <div v-if="employee?.customerList?.length" class="assets-boxes">
            <div
              v-for="(asset) in getDisplayedCustomers(employee)"
              :key="asset.id"
              class="asset-box-item"
            >
              <div class="asset-info">
                <span v-tooltip="asset.name" class="asset-name">
                  {{ asset.name }}
                </span>
              </div>
              <div class="asset-status-info">
                <span class="asset-status">
                  {{ asset.lastConnectTimeStr }}
                </span>
              </div>
            </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.customer-assets-boxes {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // 吸顶员工信息组件样式
  .temp-staff-info-box {
    position: fixed;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px 16px 8px 8px;
    height: 40px;
    box-sizing: border-box;
    background: $neutral-color-1;
    border-radius: 0 0 4px 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f0f0;
    }

    .staff-info-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 4px;
      height: 24px;
      line-height: 24px;
      .staff-info-box__avtar {
        width: 24px;
        height: 24px;
        border: 1px solid $neutral-color-2;
        border-radius: 4px;
      }

      .staff-info-box__name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .staff-info-box__department {
        font-size: 12px;
        color: #666;
      }

      .staff-info-box__red-count {
        font-size: 12px;
        color: $error-base-color-6;
        /* 错误色/Error-1 浅色白底悬浮 */
        background: #ffece8;
        padding: 2px 8px;
        height: 20px;
        line-height: 16px;
        box-sizing: border-box;
        border-radius: 4px;
        font-weight: normal;
      }
    }

    .staff-info-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .expand-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .loading-container,
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    color: #999;
    font-size: 14px;
  }

  .customer-assets-box {
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .staff-info-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      padding: 8px 16px 8px 8px;
      height: 40px;
      box-sizing: border-box;
      background: $neutral-color-1;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      &:hover {
        background: #f0f0f0;
      }

      // 吸顶样式
      &--sticky {
        position: fixed !important;
        z-index: 1000;
        background: $neutral-color-1;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        margin: 0;

        &:hover {
          background: #f0f0f0;
        }
      }

      .staff-info-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 4px;
        height: 24px;
        line-height: 24px;
        .staff-info-box__avtar {
          width: 24px;
          height: 24px;
          border: 1px solid $neutral-color-2;
          border-radius: 4px;
        }

        .staff-info-box__name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .staff-info-box__department {
          font-size: 12px;
          color: #666;
        }

        .staff-info-box__red-count {
          font-size: 12px;
          color: $error-base-color-6;
          /* 错误色/Error-1 浅色白底悬浮 */
          background: #ffece8;
          padding: 2px 8px;
          height: 20px;
          line-height: 16px;
          box-sizing: border-box;
          border-radius: 4px;
          font-weight: normal;
        }
      }

      .staff-info-right {
        display: flex;
        align-items: center;
        gap: 12px;

        .expand-icon {
          font-size: 12px;
          color: #999;
          transition: transform 0.3s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }

    .assets-boxes {
      background: #fff;
      min-height: 60px;
      max-height: 300px;
      display: flex;
      gap: 8px;
      padding-top: 8px;
      flex-wrap: wrap;
      .asset-box-item {
        box-sizing: border-box;
        border-radius: 4px;
        background: #fd5555;
        box-shadow: 0px 2px 30px 2px rgba(0, 0, 0, 0.05);
        width: 168px;
        height: 52px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 6px 12px;
        align-items: flex-start;
        gap: 2px;
        z-index: 0;
        cursor: pointer;


        &:last-child {
          border-bottom: none;
        }

        .asset-info {
          display: flex;
          flex-direction: column;
          flex: 1;
          height: 20px;
          line-height: 20px;


          .asset-name {
            font-size: 14px;
            font-weight: 500;
            color: $base-white;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .asset-contact {
            font-size: 12px;
            color: $base-white;
          }
        }

        .asset-status-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          height: 18px;
          line-height: 18px;
          color: $base-white;
        }
      }
    }
  }
}

// 折叠动画
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>
