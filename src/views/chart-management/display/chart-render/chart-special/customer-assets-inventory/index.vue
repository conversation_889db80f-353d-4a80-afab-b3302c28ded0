<script setup>
import CustomerAssetsBoxes from './customer-assets-boxes.vue'
import CustomerCategoriesBox from './cusotmer-categories-box.vue'
import CustomerAssetsFilter from './customer-assets-filter.vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { getSpecialPerformance } from '@/api/statistics'

defineOptions({
  name: 'CustomerAssetsInventory'
})

const props = defineProps({
  chartOption: {
    type: Object,
    default: () => ({})
  },
  selectMenu: {
    type: Object,
    default: () => ({})
  },
  globalStyleOption: {
    type: Object,
    default: () => ({})
  },
  templateObj: {
    type: Object,
    default: () => ({})
  }
})

// 分页配置项
const pageOption = reactive({
  currentPageNum: 1, // 当前页的页码
  pageSize: 10, // 每页的大小
  pageSizes: [10, 20, 30, 40, 50, 100],
  rowsCount: 0 // 总的条数
})

const filterForm = reactive({
  checkedId: [],
  argStrIn: '',
  argTwoId: 0,
  refId: 0
})

const categoriesChartOption = computed(() => {
  return props.chartOption?.chartList?.length === 2
    ? props.chartOption.chartList.filter((item) => {
        return item.systemCode === '10_02_01'
      })[0]
    : {}
})

const assetsChartOption = computed(() => {
  return props.chartOption?.chartList?.length === 2
    ? props.chartOption.chartList.filter((item) => {
        return item.systemCode === '10_02_02'
      })[0]
    : {}
})

const categoriesData = ref([])
const categoriesLoading = ref(false)
const currentCategory = ref('')
const currentTemplateId = ref('')

/**
 * 生成API请求参数
 * @param {boolean} isExport - 是否用于导出
 * @returns {Object} 请求参数对象
 */
const generateRequestParams = (isExport = false) => {
  const params = {
    page: pageOption.currentPageNum,
    pageSize: pageOption.pageSize,
    companyStructType: 2,
    checkSubDep: filterForm.checkSubDep,
    timeFilter: filterForm.timeRange,
    checkedId: filterForm.checkedId,
    checkedDepId: filterForm.checkedDepId || '',
    statisticsType: categoriesChartOption.value.statisticsType,
    chartIdIn: [
      {
        id: categoriesChartOption.value.id,
        statisticsType: categoriesChartOption.value.statisticsType,
        systemCode: categoriesChartOption.value.systemCode
      }
    ]
  }

  // 选择部门目标时，部门id必传，默认组织架构的第一个部门id
  if (params.companyStructType === 2 && params.checkedDepId === '') {
    params.checkedDepId = 1
  }

  return params
}

/**
 * @description: 加载客户分类数据
 * @return {Promise<void>}
 */
const loadCategoriesData = async () => {
  categoriesLoading.value = true
  const params = generateRequestParams()
  try {
    const { result, code } = await getSpecialPerformance(params)
    if(code === 1) {
      categoriesData.value = result.chartList[0].redCustomerRateList
      filterForm.argStrIn = result.chartList[0].redCustomerRateList[0].scaleId
    }

  } catch (error) {
    console.error('加载客户分类数据失败:', error)
  } finally {
    categoriesLoading.value = false
  }
}





/**
 * @description 筛选条件变更
 * @param filterForm
 */
const changeFilterForm = (newFilterForm, refreshData = false) => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = newFilterForm[key]
  })
  if(refreshData) {
    loadCategoriesData()
  }
}
/**
 * @description 切换当前选中的客户分类
 * @param categoryId
 */
const changeCurrentCategory = (categoryId) => {
  changeFilterForm({
    ...filterForm,
    argStrIn: categoryId
  })
}



onMounted(() => {
  loadCategoriesData()
  filterForm.refId = props.templateObj.customerTemplate[0].formId
})
</script>

<template>
  <div class="customer-asset-inventory__wrapper">
    <div class="filter-box__wrapper">
      <customer-assets-filter
        :filter-form="filterForm"
        :template-list="templateObj.customerTemplate"
        @filter-change="changeFilterForm"
      />
    </div>
    <div class="categories-box__wrapper">
      <customer-categories-box
        :categories-data="categoriesData"
        :filter-form="filterForm"
        :loading="categoriesLoading"
        @change-current-category="changeCurrentCategory"
      />
    </div>
    <div class="customer-assets-boxes__wrapper">
      <customer-assets-boxes
        :chart-explain="assetsChartOption"
        :filter-form="filterForm"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.customer-asset-inventory__wrapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  .filter-box__wrapper {
    height: 32px;
    line-height: 32px;
    width: 100%;
    display: flex;
    margin-bottom: 16px;
  }
  .categories-box__wrapper {
    height: 84px;
    width: 100%;
    margin-bottom: 16px;
    overflow-x: auto; /* 只在需要时显示滚动条 */
    box-sizing: border-box;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      height: 6px; /* 减小滚动条高度 */
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .customer-assets-boxes__wrapper {
    width: 100%;
    height: calc(100% - 142px);
    overflow: auto;
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      height: 6px; /* 减小滚动条高度 */
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
</style>
