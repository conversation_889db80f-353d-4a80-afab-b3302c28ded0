<!--
 * @Description: 多选标签回显
 -->

<template>
  <div class="multi-tag" :class="{ 'multi-tag__disabled': !closable }" @click="click">
    <span
      v-if="placeholderBoolean && selectedTag.length == 0"
      class="placeholder"
      :style="isBi ? 'color:#959799' : ''"
      >{{ isBi ? '全公司' : placeholder }}
      <i
        v-if="isBi"
        :class="isBi ? 'icon-arrow-down-s-line t-iconfont' : 'arrow-icon el-icon-caret-bottom'"
      ></i>
    </span>

    <el-tag
      v-for="tag in mulTags"
      v-else-if="!isBi"
      :key="tag.id"
      :closable="closable"
      @close.stop="handleClose(tag)"
    >
      <span v-html="addNodeByWX(tag[propFormat.label], tag.property === 'dept' ? 1 : 0)"></span>
    </el-tag>
    <span v-else-if="isBi" class="bi_showSelect">
      <span>
        {{
          selectedTag.length > 1
            ? selectedTag[0].name + '等' + selectedTag.length + '人'
            : selectedTag[0].name
        }}
      </span>
      <i :class="isBi ? 'icon-arrow-down-s-line t-iconfont' : 'arrow-icon el-icon-caret-bottom'"></i
    ></span>
    <span v-if="selectedTag.length > 1 && !isBi" class="tagsNum">{{
      '+' + (selectedTag.length - 1)
    }}</span>

    <slot></slot>
  </div>
</template>

<script>
import i18n from '@/lang'

export default {
  name: 'MultiTag',

  props: {
    selectedTag: {
      type: Array,
      default: () => {
        return []
      }
    },
    placeholder: {
      type: String,
      default: i18n.t('placeholder.choosePls', { attr: '' })
    },
    closable: {
      type: Boolean,
      default: true
    },
    propFormat: {
      type: Object,
      default: () => {
        return { label: 'name' }
      }
    },
    placeholderBoolean: {
      type: Boolean,
      default: true
    },
    isBi: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    mulTags() {
      return this.selectedTag.slice(0, 1)
    }
  },

  methods: {
    handleClose(tag) {
      this.$emit('tagDelete', tag)
    },
    click() {
      this.$emit('click')
    },
    thisTag(tag) {
      return tag && tag.length > 15 ? tag.substring(0, 14) + '...' : tag
    }
  }
}
</script>

<style lang="scss" scoped>
.multi-tag {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  min-height: 30px;
  m
  cursor: pointer;
  background-color: $base-white;
  border: 1px solid $line-input;
  border-radius: 4px;
  .placeholder {
    padding: 0 30px 0 10px;
    font-size: 14px;
    line-height: 30px;
    color: $text-grey;
  }
  .el-tag {
    margin: 3px 5px;
    color: $text-plain;
    background-color: $neutral-color-1;
    border-color: $neutral-color-1;
    .el-tag__close,
    .el-icon-close {
      color: $text-auxiliary;
    }
  }
  .tagsNum {
    box-sizing: border-box;
    display: inline-block;
    height: 24px;
    padding: 0 8px;
    margin: 3px 5px;
    line-height: 22px;
    color: $text-auxiliary;
    white-space: nowrap;
    background-color: $bg-tag;
    border: 1px solid $neutral-color-3;
    border-radius: 4px;
  }
}
.multi-tag__disabled {
  color: $text-grey;
  cursor: not-allowed;
  background-color: $neutral-color-1;
  border-color: $neutral-color-3;
}
.arrow-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 13px;
  color: $text-grey;
}
.bi_showSelect {
  padding: 0 4px;
  margin-right: 12px;
}
</style>

<style lang="scss">
.multi-tag {
  .el-tag {
    .el-tag__close,
    .el-icon-close {
      color: $text-auxiliary;
      &:hover {
        background-color: $bg-table;
      }
    }
  }
}
</style>
